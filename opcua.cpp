#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <locale>
#include <algorithm>
#include <cctype>
#ifdef USE_OPEN62541 // 使用open62541库
#include <winsock2.h>  // 必须在windows.h之前包含
#include <open62541/client.h>
#include <open62541/client_config_default.h>
#include <open62541/client_highlevel.h>
#include <open62541/plugin/log_stdout.h>
#endif

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

class OPCUAClientConfig {
public:
    // 服务器连接配置
    std::string serverUrl = "opc.tcp://*************:4841/OPCUA/KepOPC";  // 服务器URL
    std::string serverName = "OPC UA Server";            // 服务器名称
    int serverPort = 4840;                               // 服务器端口
    std::string serverHost = "localhost";                // 服务器主机

    // 安全配置
    std::string securityPolicy = "None";                 // 安全策略: None, Basic128Rsa15, Basic256, Basic256Sha256
    std::string securityMode = "None";                   // 安全模式: None, Sign, SignAndEncrypt
    std::string certificatePath = "";                    // 客户端证书路径
    std::string privateKeyPath = "";                     // 客户端私钥路径
    std::string trustListPath = "";                      // 信任列表路径

    // 认证配置
    bool useAuthentication = false;                      // 是否使用认证
    std::string username = "";                           // 用户名
    std::string password = "";                           // 密码
    std::string authenticationMethod = "Anonymous";      // 认证方法: Anonymous, UserName, Certificate

    // 连接配置
    int connectionTimeout = 5000;                        // 连接超时时间(毫秒)
    int sessionTimeout = 60000;                          // 会话超时时间(毫秒)
    int requestTimeout = 10000;                          // 请求超时时间(毫秒)
    int maxRetryCount = 3;                               // 最大重试次数
    int retryDelay = 1000;                               // 重试延迟(毫秒)

    // 订阅配置
    double publishingInterval = 1000.0;                  // 发布间隔(毫秒)
    int maxNotificationsPerPublish = 10;                 // 每次发布的最大通知数
    int lifetimeCount = 10000;                           // 生命周期计数
    int maxKeepAliveCount = 3;                           // 最大保活计数
    int priority = 0;                                    // 优先级

    // 监控项配置
    double samplingInterval = 1000.0;                    // 采样间隔(毫秒)
    int queueSize = 10;                                  // 队列大小
    bool discardOldest = true;                           // 是否丢弃最旧的数据

    // 节点配置 - 简化的节点ID列表
    std::vector<std::string> nodeIds = {
        "ns=0;i=11565",  // MaxNodesPerRead
        "ns=0;i=11567",  // MaxNodesPerWrite
        "ns=0;i=11570",  // MaxNodesPerBrowse
        "ns=0;i=11574"   // MaxMonitoredItemsPerCall
        // 注意：如果这些节点不可用，程序会自动浏览服务器查找可用节点
    };

    // 应用程序配置
    std::string applicationName = "OPC UA C++ Client";   // 应用程序名称

    // 日志配置
    bool enableLogging = false;                          // 是否启用日志（设为false减少输出）
    std::string logLevel = "ERROR";                      // 日志级别: ERROR, WARNING, INFO, DEBUG

    // 其他配置
    bool autoReconnect = true;                           // 是否自动重连
};

class OPCUAClient {
private:
    OPCUAClientConfig config;
    bool connected = false;

#ifdef USE_OPEN62541
    UA_Client* client = nullptr;
    UA_ClientConfig* clientConfig = nullptr;
#endif

public:
    OPCUAClient(const OPCUAClientConfig& cfg) : config(cfg) {
        initializeClient();
    }

    ~OPCUAClient() {
        disconnect();
        cleanup();
    }

    void initializeClient() {
#ifdef USE_OPEN62541
        client = UA_Client_new();
        clientConfig = UA_Client_getConfig(client);

        // 设置默认配置
        UA_ClientConfig_setDefault(clientConfig);

        // 应用配置参数
        clientConfig->timeout = config.connectionTimeout;

        // 设置应用程序描述
        clientConfig->clientDescription.applicationName =
            UA_LOCALIZEDTEXT((char*)"en-US", (char*)config.applicationName.c_str());

        // 禁用日志输出以避免乱码问题
        if (config.enableLogging) {
            // 设置日志级别
            UA_LogLevel logLevel = UA_LOGLEVEL_ERROR;  // 只显示错误信息
            if (config.logLevel == "TRACE") logLevel = UA_LOGLEVEL_TRACE;
            else if (config.logLevel == "DEBUG") logLevel = UA_LOGLEVEL_DEBUG;
            else if (config.logLevel == "WARNING") logLevel = UA_LOGLEVEL_WARNING;
            else if (config.logLevel == "ERROR") logLevel = UA_LOGLEVEL_ERROR;
            else if (config.logLevel == "FATAL") logLevel = UA_LOGLEVEL_FATAL;

            clientConfig->logger = UA_Log_Stdout_withLevel(logLevel);
        } else {
            // 设置最高日志级别来减少输出
            clientConfig->logger = UA_Log_Stdout_withLevel(UA_LOGLEVEL_FATAL);
        }
#endif

        std::cout << "OPC UA客户端初始化完成" << std::endl;
        printConfiguration();
    }

    bool connect() {
        std::cout << "正在连接到服务器: " << config.serverUrl << std::endl;

#ifdef USE_OPEN62541
        UA_StatusCode retval;

        if (config.useAuthentication && config.authenticationMethod == "UserName") {
            // 使用用户名密码认证
            retval = UA_Client_connectUsername(client, config.serverUrl.c_str(),
                                             config.username.c_str(), config.password.c_str());
        } else {
            // 匿名连接
            retval = UA_Client_connect(client, config.serverUrl.c_str());
        }

        if (retval == UA_STATUSCODE_GOOD) {
            connected = true;
            std::cout << "成功连接到服务器!" << std::endl;
            return true;
        } else {
            std::cout << "连接失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
        std::cout << "请安装open62541库并使用 -DUSE_OPEN62541 编译选项" << std::endl;
        return false;
#endif
    }

    void disconnect() {
        if (connected) {
            std::cout << "正在断开连接..." << std::endl;

#ifdef USE_OPEN62541
            UA_Client_disconnect(client);
#endif
            connected = false;
            std::cout << "已断开连接" << std::endl;
        }
    }

    // 简化的NodeId解析函数
    UA_NodeId parseNodeId(const std::string& nodeIdStr) {
#ifdef USE_OPEN62541
        // 简单的正则表达式风格解析，减少复杂的条件判断
        if (nodeIdStr.empty()) {
            return UA_NODEID_NULL;
        }

        try {
            // 标准格式: ns=X;i=Y 或 ns=X;s=Y
            if (nodeIdStr.substr(0, 3) == "ns=") {
                size_t semicolon = nodeIdStr.find(';');
                if (semicolon == std::string::npos) return UA_NODEID_NULL;

                uint16_t ns = std::stoul(nodeIdStr.substr(3, semicolon - 3));
                std::string idPart = nodeIdStr.substr(semicolon + 1);

                if (idPart.substr(0, 2) == "i=") {
                    // 数字型
                    uint32_t id = std::stoul(idPart.substr(2));
                    return UA_NODEID_NUMERIC(ns, id);
                } else if (idPart.substr(0, 2) == "s=") {
                    // 字符串型
                    return UA_NODEID_STRING_ALLOC(ns, idPart.substr(2).c_str());
                }
            }
            // 简化格式: i=Y 或 s=Y (默认namespace 0)
            else if (nodeIdStr.substr(0, 2) == "i=") {
                uint32_t id = std::stoul(nodeIdStr.substr(2));
                return UA_NODEID_NUMERIC(0, id);
            } else if (nodeIdStr.substr(0, 2) == "s=") {
                return UA_NODEID_STRING_ALLOC(0, nodeIdStr.substr(2).c_str());
            }
            // 纯数字 (默认namespace 0)
            else {
                uint32_t id = std::stoul(nodeIdStr);
                return UA_NODEID_NUMERIC(0, id);
            }
        } catch (...) {
            // 解析失败，作为字符串处理 (默认namespace 0)
            return UA_NODEID_STRING_ALLOC(0, nodeIdStr.c_str());
        }
#endif
        return UA_NODEID_NULL;
    }

    // 简化的值显示函数
    void printVariantValue(const UA_Variant& value) {
#ifdef USE_OPEN62541
        if (value.data == nullptr) {
            std::cout << "[空值]";
            return;
        }

        // 使用函数指针表简化类型检查
        if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_DOUBLE])) {
            std::cout << *(UA_Double*)value.data;
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_FLOAT])) {
            std::cout << *(UA_Float*)value.data;
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_INT32])) {
            std::cout << *(UA_Int32*)value.data;
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_UINT32])) {
            std::cout << *(UA_UInt32*)value.data;
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_INT64])) {
            std::cout << *(UA_Int64*)value.data;
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_UINT64])) {
            std::cout << *(UA_UInt64*)value.data;
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_BOOLEAN])) {
            std::cout << (*(UA_Boolean*)value.data ? "true" : "false");
        } else if (UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_STRING])) {
            UA_String* str = (UA_String*)value.data;
            if (str && str->data) {
                std::cout << std::string(reinterpret_cast<char*>(str->data), str->length);
            } else {
                std::cout << "[空字符串]";
            }
        } else {
            std::cout << "[复杂类型]";
        }
#endif
    }

    bool readNode(const std::string& nodeId) {
        if (!connected) {
            std::cout << "错误: 客户端未连接" << std::endl;
            return false;
        }

#ifdef USE_OPEN62541
        UA_NodeId parsedNodeId = parseNodeId(nodeId);
        if (UA_NodeId_isNull(&parsedNodeId)) {
            std::cout << "读取失败: NodeId格式错误 - " << nodeId << std::endl;
            return false;
        }

        UA_Variant value;
        UA_Variant_init(&value);
        UA_StatusCode retval = UA_Client_readValueAttribute(client, parsedNodeId, &value);

        std::cout << "节点 " << nodeId << ": ";
        if (retval == UA_STATUSCODE_GOOD) {
            printVariantValue(value);
            std::cout << " ✓" << std::endl;
        } else {
            std::cout << "读取失败 (" << UA_StatusCode_name(retval) << ")" << std::endl;
        }

        // 清理资源
        UA_Variant_clear(&value);
        if (parsedNodeId.identifierType == UA_NODEIDTYPE_STRING) {
            UA_NodeId_clear(&parsedNodeId);
        }

        return retval == UA_STATUSCODE_GOOD;
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
        return false;
#endif
    }



    void browseServerNodes() {
#ifdef USE_OPEN62541
        std::cout << "\n=== 浏览服务器节点结构 ===" << std::endl;

        // 浏览Objects文件夹
        UA_NodeId objectsNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_OBJECTSFOLDER);
        browseNode(objectsNodeId, "Objects", 0, 3);

        // 浏览Server对象
        UA_NodeId serverNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_SERVER);
        browseNode(serverNodeId, "Server", 0, 1);

        std::cout << "=== 节点浏览完成 ===\n" << std::endl;
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
#endif
    }

    // 简化的NodeId字符串构建函数
    std::string buildNodeIdString(const UA_NodeId& nodeId) {
#ifdef USE_OPEN62541
        if (nodeId.identifierType == UA_NODEIDTYPE_NUMERIC) {
            return "ns=" + std::to_string(nodeId.namespaceIndex) + ";i=" + std::to_string(nodeId.identifier.numeric);
        } else if (nodeId.identifierType == UA_NODEIDTYPE_STRING) {
            return "ns=" + std::to_string(nodeId.namespaceIndex) + ";s=" +
                   std::string((char*)nodeId.identifier.string.data, nodeId.identifier.string.length);
        }
#endif
        return "unknown";
    }

    bool tryReadVariableValue(const UA_NodeId& nodeId) {
#ifdef USE_OPEN62541
        UA_Variant value;
        UA_Variant_init(&value);
        UA_StatusCode retval = UA_Client_readValueAttribute(client, nodeId, &value);

        if (retval == UA_STATUSCODE_GOOD) {
            std::cout << " = ";
            printVariantValue(value);
            UA_Variant_clear(&value);
            return true;
        }
        UA_Variant_clear(&value);
        return false;
#else
        return false;
#endif
    }

    void browseNode(UA_NodeId nodeId, const std::string& nodeName, int depth, int maxDepth) {
#ifdef USE_OPEN62541
        if (depth > maxDepth) return;

        // 打印当前节点
        for (int i = 0; i < depth; i++) std::cout << "  ";
        std::cout << nodeName << std::endl;

        UA_BrowseRequest browseRequest;
        UA_BrowseRequest_init(&browseRequest);
        browseRequest.requestedMaxReferencesPerNode = 0;
        browseRequest.nodesToBrowse = UA_BrowseDescription_new();
        browseRequest.nodesToBrowseSize = 1;
        browseRequest.nodesToBrowse[0].nodeId = nodeId;
        browseRequest.nodesToBrowse[0].resultMask = UA_BROWSERESULTMASK_ALL;

        UA_BrowseResponse browseResponse = UA_Client_Service_browse(client, browseRequest);

        if (browseResponse.responseHeader.serviceResult == UA_STATUSCODE_GOOD) {
            for (size_t i = 0; i < browseResponse.resultsSize; ++i) {
                for (size_t j = 0; j < browseResponse.results[i].referencesSize; ++j) {
                    UA_ReferenceDescription* ref = &browseResponse.results[i].references[j];

                    // 使用简化的NodeId构建函数
                    std::string nodeIdStr = buildNodeIdString(ref->nodeId.nodeId);

                    // 获取显示名称
                    std::string displayName = "Unknown";
                    if (ref->displayName.text.data) {
                        displayName = std::string((char*)ref->displayName.text.data, ref->displayName.text.length);
                    }

                    // 简化的节点类型检查
                    std::string nodeTypeInfo = "";
                    if (ref->nodeClass == UA_NODECLASS_VARIABLE) {
                        nodeTypeInfo = " [变量]";
                        if (tryReadVariableValue(ref->nodeId.nodeId)) {
                            nodeTypeInfo += " ✓";
                        } else {
                            nodeTypeInfo += " ✗";
                        }
                    } else if (ref->nodeClass == UA_NODECLASS_OBJECT) {
                        nodeTypeInfo = " [对象]";
                    } else if (ref->nodeClass == UA_NODECLASS_METHOD) {
                        nodeTypeInfo = " [方法]";
                    }

                    // 打印节点信息
                    for (int k = 0; k < depth + 1; k++) std::cout << "  ";
                    std::cout << "├─ " << displayName << " [" << nodeIdStr << "]" << nodeTypeInfo << std::endl;

                    // 递归浏览子节点
                    if (depth < maxDepth) {
                        browseNode(ref->nodeId.nodeId, displayName, depth + 1, maxDepth);
                    }
                }
            }
        }

        UA_BrowseRequest_clear(&browseRequest);
        UA_BrowseResponse_clear(&browseResponse);
#endif
    }

    void readAllConfiguredNodes() {
        std::cout << "\n=== 读取配置的节点 (" << config.nodeIds.size() << "个) ===" << std::endl;

        int successCount = 0;
        for (const auto& nodeId : config.nodeIds) {
            if (readNode(nodeId)) {
                successCount++;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        std::cout << "\n结果: " << successCount << "/" << config.nodeIds.size() << " 个节点读取成功" << std::endl;

        // 如果没有成功读取任何节点，则浏览服务器
        if (successCount == 0 && !config.nodeIds.empty()) {
            std::cout << "\n未找到可读取的配置节点，开始浏览服务器..." << std::endl;
            browseServerNodes();
            findReadableVariables();
        }
    }

    void findReadableVariables() {
#ifdef USE_OPEN62541
        std::cout << "正在搜索可读取的变量节点..." << std::endl;
        std::vector<std::string> readableNodes;
        std::vector<std::string> matchingNodes;  // 匹配您配置关键词的节点

        // 从Objects文件夹开始搜索
        UA_NodeId objectsNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_OBJECTSFOLDER);
        searchReadableVariables(objectsNodeId, readableNodes, matchingNodes, 0, 5);

        // 首先显示匹配的节点
        if (!matchingNodes.empty()) {
            std::cout << "\n找到 " << matchingNodes.size() << " 个匹配您配置关键词的可读取节点:" << std::endl;
            for (size_t i = 0; i < matchingNodes.size(); i++) {
                std::cout << "  ★ " << (i + 1) << ". " << matchingNodes[i] << std::endl;
            }
        }

        if (!readableNodes.empty()) {
            std::cout << "\n找到 " << readableNodes.size() << " 个可读取的变量节点:" << std::endl;
            for (size_t i = 0; i < readableNodes.size() && i < 15; i++) {  // 最多显示15个
                std::cout << "  " << (i + 1) << ". " << readableNodes[i] << std::endl;
            }
            if (readableNodes.size() > 15) {
                std::cout << "  ... 还有 " << (readableNodes.size() - 15) << " 个节点" << std::endl;
            }

            std::cout << "\n建议: 您可以将这些节点ID添加到配置中进行监控。" << std::endl;
            if (!matchingNodes.empty()) {
                std::cout << "特别推荐: 标有 ★ 的节点可能是您要找的节点。" << std::endl;
            }
        } else {
            std::cout << "未找到可读取的变量节点。" << std::endl;
        }
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
#endif
    }

    // 检查是否匹配关键词的辅助函数
    bool matchesKeywords(const std::string& text) {
        std::string lower = text;
        std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);

        const std::vector<std::string> keywords = {"a2", "baojing", "f11", "tag3", "group1", "root"};
        for (const auto& keyword : keywords) {
            if (lower.find(keyword) != std::string::npos) {
                return true;
            }
        }
        return false;
    }

    void searchReadableVariables(UA_NodeId nodeId, std::vector<std::string>& readableNodes,
                                std::vector<std::string>& matchingNodes, int depth, int maxDepth) {
#ifdef USE_OPEN62541
        if (depth > maxDepth || readableNodes.size() >= 100) return;

        UA_BrowseRequest browseRequest;
        UA_BrowseRequest_init(&browseRequest);
        browseRequest.requestedMaxReferencesPerNode = 0;
        browseRequest.nodesToBrowse = UA_BrowseDescription_new();
        browseRequest.nodesToBrowseSize = 1;
        browseRequest.nodesToBrowse[0].nodeId = nodeId;
        browseRequest.nodesToBrowse[0].resultMask = UA_BROWSERESULTMASK_ALL;

        UA_BrowseResponse browseResponse = UA_Client_Service_browse(client, browseRequest);

        if (browseResponse.responseHeader.serviceResult == UA_STATUSCODE_GOOD) {
            for (size_t i = 0; i < browseResponse.resultsSize; ++i) {
                for (size_t j = 0; j < browseResponse.results[i].referencesSize; ++j) {
                    UA_ReferenceDescription* ref = &browseResponse.results[i].references[j];

                    if (ref->nodeClass == UA_NODECLASS_VARIABLE) {
                        // 使用简化的NodeId构建
                        std::string nodeIdStr = buildNodeIdString(ref->nodeId.nodeId);

                        // 尝试读取变量值
                        if (tryReadVariableValue(ref->nodeId.nodeId)) {
                            std::string displayName = "Unknown";
                            if (ref->displayName.text.data) {
                                displayName = std::string((char*)ref->displayName.text.data, ref->displayName.text.length);
                            }

                            std::string fullInfo = nodeIdStr + " (" + displayName + ")";
                            readableNodes.push_back(fullInfo);

                            // 检查是否匹配关键词
                            if (matchesKeywords(displayName) || matchesKeywords(nodeIdStr)) {
                                matchingNodes.push_back(fullInfo);
                            }
                        }
                    }

                    // 递归搜索子节点
                    if (depth < maxDepth) {
                        searchReadableVariables(ref->nodeId.nodeId, readableNodes, matchingNodes, depth + 1, maxDepth);
                    }
                }
            }
        }

        UA_BrowseRequest_clear(&browseRequest);
        UA_BrowseResponse_clear(&browseResponse);
#endif
    }

    void printConfiguration() {
        std::cout << "\n=== OPC UA客户端配置 ===" << std::endl;
        std::cout << "服务器URL: " << config.serverUrl << std::endl;
        std::cout << "服务器名称: " << config.serverName << std::endl;
        std::cout << "安全策略: " << config.securityPolicy << std::endl;
        std::cout << "安全模式: " << config.securityMode << std::endl;
        std::cout << "认证方法: " << config.authenticationMethod << std::endl;
        std::cout << "连接超时: " << config.connectionTimeout << "ms" << std::endl;
        std::cout << "会话超时: " << config.sessionTimeout << "ms" << std::endl;
        std::cout << "应用程序名称: " << config.applicationName << std::endl;
        std::cout << "监控节点数量: " << config.nodeIds.size() << std::endl;

        if (!config.nodeIds.empty()) {
            std::cout << "配置的节点列表:" << std::endl;
            for (size_t i = 0; i < config.nodeIds.size(); i++) {
                std::cout << "  " << (i + 1) << ". " << config.nodeIds[i] << std::endl;
            }
        }

        std::cout << "自动重连: " << (config.autoReconnect ? "是" : "否") << std::endl;
        std::cout << "========================\n" << std::endl;
    }

    void cleanup() {
#ifdef USE_OPEN62541
        if (client) {
            UA_Client_delete(client);
            client = nullptr;
        }
#endif
    }

    bool isConnected() const {
        return connected;
    }

    // 获取和设置配置的方法
    OPCUAClientConfig& getConfig() {
        return config;
    }

    void setConfig(const OPCUAClientConfig& newConfig) {
        config = newConfig;
    }
};

// 基本使用示例
void basicUsageExample() {
    std::cout << "=== OPC UA 客户端基本使用 ===" << std::endl;

    // 创建默认配置
    OPCUAClientConfig config;

    // 在这里修改您的服务器配置
    // config.serverUrl = "opc.tcp://192.168.1.100:4840";
    // config.username = "admin";
    // config.password = "password";
    // config.useAuthentication = true;
    // config.authenticationMethod = "UserName";

    // 创建客户端
    OPCUAClient client(config);

    // 尝试连接到服务器
    if (client.connect()) {
        std::cout << "连接成功！" << std::endl;

        // 检查是否有配置的节点
        if (!config.nodeIds.empty()) {
            std::cout << "使用配置的节点列表..." << std::endl;
            // 读取所有配置的节点
            client.readAllConfiguredNodes();
        } else {
            std::cout << "配置的节点列表为空，开始浏览服务器..." << std::endl;
            // 浏览服务器节点结构
            client.browseServerNodes();
        }

        // 等待用户输入，保持程序运行
        std::cout << "\n=== 程序运行中 ===" << std::endl;
        std::cout << "按 Enter 键退出程序..." << std::endl;
        std::cin.get();  // 等待用户按回车键

        // 断开连接
        client.disconnect();
    } else {
        std::cout << "连接失败！请检查服务器配置和网络连接。" << std::endl;
    }
}

// 设置控制台编码支持中文
void setupConsoleEncoding() {
#ifdef _WIN32
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台模式支持UTF-8
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD dwMode = 0;
    GetConsoleMode(hOut, &dwMode);
    dwMode |= ENABLE_PROCESSED_OUTPUT | ENABLE_WRAP_AT_EOL_OUTPUT | ENABLE_VIRTUAL_TERMINAL_PROCESSING;
    SetConsoleMode(hOut, dwMode);

    // 尝试设置中文locale，如果失败则使用默认
    try {
        std::locale::global(std::locale("zh_CN.UTF-8"));
    } catch (...) {
        try {
            std::locale::global(std::locale("Chinese"));
        } catch (...) {
            // 如果都失败，使用默认locale
            std::locale::global(std::locale("C"));
        }
    }
#endif
}

int main() {
    // 设置控制台编码
    setupConsoleEncoding();

    std::cout << "OPC UA C++ 客户端程序" << std::endl;
    std::cout << "=====================" << std::endl;

    try {
        // 运行基本使用示例
        basicUsageExample();

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n程序执行完成。" << std::endl;
    std::cout << "\n配置说明：" << std::endl;
    std::cout << "1. 修改 OPCUAClientConfig 类中的默认值来配置您的服务器" << std::endl;
    std::cout << "2. 安装 open62541 库并使用 -DUSE_OPEN62541 编译选项启用OPC UA功能" << std::endl;
    std::cout << "3. 根据您的服务器调整 serverUrl、节点ID、安全设置和认证参数" << std::endl;

    return 0;
}